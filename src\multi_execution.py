#!/usr/bin/env python3
"""
Multi-Execution System for AI Systems
Enables elegant definition and execution of multiple prompt variations
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator

from main import execute_sequence, ExecutorConfig, TemplateCatalog, SequenceManager, Config, PromptParser
from config import EXECUTION_DEFAULTS


# =============================================================================
# MULTI-EXECUTION CONFIGURATION MODELS
# =============================================================================

class ExecutionVariant(BaseModel):
    """Configuration for a single execution variant."""
    name: str = Field(description="Human-readable name for this variant")
    sequence: str = Field(description="Sequence specification (e.g., '1000', '4200|1200')")
    models: Optional[List[str]] = Field(default=None, description="Models to use (inherits from parent if None)")
    
    # Optional execution parameters
    chain_mode: Optional[bool] = Field(default=None, description="Enable/disable chain mode")
    aggregator: Optional[str] = Field(default=None, description="Aggregator sequence specification")
    aggregator_inputs: Optional[List[str]] = Field(default=None, description="Aggregator input step IDs")
    temperature: Optional[float] = Field(default=None, description="Model temperature")
    max_tokens: Optional[int] = Field(default=None, description="Maximum tokens")
    
    # Output configuration
    output_suffix: Optional[str] = Field(default=None, description="Custom output file suffix")


class MultiExecutionConfig(BaseModel):
    """Configuration for multi-execution with shared prompt."""
    version: str = Field(default="1.0", description="Multi-execution config version")
    name: str = Field(description="Multi-execution name")
    description: Optional[str] = Field(default=None, description="Multi-execution description")
    
    # Shared configuration
    base_prompt: str = Field(description="Base prompt used for all variants")
    default_models: List[str] = Field(description="Default models for all variants")
    
    # Global defaults
    defaults: Dict[str, Any] = Field(default_factory=dict, description="Default execution parameters")
    
    # Execution variants
    variants: List[ExecutionVariant] = Field(description="List of execution variants")
    
    # Execution options
    concurrent: bool = Field(default=True, description="Run variants concurrently")
    max_concurrent: Optional[int] = Field(default=None, description="Maximum concurrent variants")
    
    @validator('variants')
    def validate_variants(cls, v):
        if not v or len(v) == 0:
            raise ValueError("At least one variant must be specified")
        
        # Check for duplicate variant names
        names = [variant.name for variant in v]
        if len(names) != len(set(names)):
            raise ValueError("Variant names must be unique")
        
        return v


# =============================================================================
# MULTI-EXECUTION BUILDER
# =============================================================================

class MultiExecutionBuilder:
    """Builder pattern for creating multi-execution configurations."""
    
    def __init__(self, name: str, base_prompt: str):
        self.config = {
            "version": "1.0",
            "name": name,
            "base_prompt": base_prompt,
            "default_models": ["gpt-3.5-turbo"],
            "defaults": {},
            "variants": [],
            "concurrent": True
        }
    
    def with_default_models(self, models: List[str]) -> 'MultiExecutionBuilder':
        """Set default models for all variants."""
        self.config["default_models"] = models
        return self
    
    def with_defaults(self, **kwargs) -> 'MultiExecutionBuilder':
        """Set default execution parameters."""
        self.config["defaults"].update(kwargs)
        return self
    
    def with_description(self, description: str) -> 'MultiExecutionBuilder':
        """Set description."""
        self.config["description"] = description
        return self
    
    def with_concurrency(self, concurrent: bool = True, max_concurrent: Optional[int] = None) -> 'MultiExecutionBuilder':
        """Configure concurrency settings."""
        self.config["concurrent"] = concurrent
        if max_concurrent:
            self.config["max_concurrent"] = max_concurrent
        return self
    
    def add_variant(self, name: str, sequence: str, **kwargs) -> 'MultiExecutionBuilder':
        """Add an execution variant."""
        variant = {"name": name, "sequence": sequence}
        variant.update(kwargs)
        self.config["variants"].append(variant)
        return self
    
    def add_template_series(self, base_name: str, sequences: List[str], **kwargs) -> 'MultiExecutionBuilder':
        """Add multiple variants for a series of templates."""
        for i, sequence in enumerate(sequences, 1):
            variant_name = f"{base_name}_{i:02d}"
            self.add_variant(variant_name, sequence, **kwargs)
        return self
    
    def build(self) -> MultiExecutionConfig:
        """Build the final configuration."""
        return MultiExecutionConfig(**self.config)
    
    def save(self, path: Union[str, Path]) -> Path:
        """Save configuration to JSON file."""
        config = self.build()
        path = Path(path)
        
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(config.dict(), f, indent=2, ensure_ascii=False)
        
        return path


# =============================================================================
# MULTI-EXECUTION EXECUTOR
# =============================================================================

class MultiExecutor:
    """Executes multiple variants of the same prompt with different templates."""
    
    def __init__(self, config: Union[str, Path, MultiExecutionConfig]):
        if isinstance(config, (str, Path)):
            self.config_path = Path(config)
            self.multi_config: Optional[MultiExecutionConfig] = None
        else:
            self.config_path = None
            self.multi_config = config
        
        self.catalog = None
        self.results: Dict[str, Any] = {}
    
    def load_config(self) -> MultiExecutionConfig:
        """Load and validate multi-execution configuration."""
        if self.multi_config:
            return self.multi_config
        
        if not self.config_path.exists():
            raise FileNotFoundError(f"Multi-execution config file not found: {self.config_path}")
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            self.multi_config = MultiExecutionConfig(**config_data)
            return self.multi_config
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in multi-execution config: {e}")
        except Exception as e:
            raise ValueError(f"Invalid multi-execution configuration: {e}")
    
    def load_catalog(self):
        """Load template catalog."""
        self.catalog = TemplateCatalog.load_catalog()
        if not self.catalog:
            raise RuntimeError("Failed to load template catalog")
    
    async def execute_variant(self, variant: ExecutionVariant, variant_index: int) -> Dict[str, Any]:
        """Execute a single variant."""
        print(f"\n🎯 Starting variant '{variant.name}' ({variant_index + 1}/{len(self.multi_config.variants)})")
        
        try:
            # Parse the base prompt for any embedded specifications
            cleaned_prompt, embedded_sequence, embedded_models = PromptParser.extract_all_from_prompt(
                self.multi_config.base_prompt
            )
            
            # Use embedded sequence if present, otherwise use variant sequence
            final_sequence = embedded_sequence or variant.sequence
            
            # Resolve sequence steps
            sequence_steps = SequenceManager.resolve_sequence_specification(
                self.catalog, final_sequence
            )
            
            if not sequence_steps:
                raise ValueError(f"Sequence '{final_sequence}' not found")
            
            # Determine models to use
            final_models = (
                embedded_models or 
                variant.models or 
                self.multi_config.default_models
            )
            
            # Apply defaults and variant-specific settings
            defaults = self.multi_config.defaults
            
            # Generate output filename
            timestamp = datetime.now().strftime("%Y.%m.%d-kl.%H.%M.%S")
            suffix = variant.output_suffix or variant.name.replace(' ', '_')
            output_filename = f"multi--{timestamp}--{suffix}--{final_sequence.replace('|', '_')}.json"
            output_path = Path("src/output") / output_filename
            
            # Create executor configuration
            config = ExecutorConfig(
                sequence_steps=sequence_steps,
                user_prompt=cleaned_prompt,
                sequence_id=final_sequence,
                models=final_models,
                output_file=str(output_path),
                system_instruction_extractor=TemplateCatalog.get_system_instruction,
                
                # Execution parameters (variant overrides defaults)
                chain_mode=variant.chain_mode if variant.chain_mode is not None else defaults.get('chain_mode', EXECUTION_DEFAULTS['chain_mode']),
                show_inputs=defaults.get('show_inputs', EXECUTION_DEFAULTS['show_inputs']),
                show_system_instructions=defaults.get('show_system_instructions', EXECUTION_DEFAULTS['show_system_instructions']),
                show_responses=defaults.get('show_responses', EXECUTION_DEFAULTS['show_responses']),
                minified_output=defaults.get('minified_output', EXECUTION_DEFAULTS['minified_output']),
                
                # Optional parameters
                aggregator=variant.aggregator,
                aggregator_inputs=variant.aggregator_inputs,
                temperature=variant.temperature if variant.temperature is not None else defaults.get('temperature'),
                max_tokens=variant.max_tokens if variant.max_tokens is not None else defaults.get('max_tokens')
            )
            
            # Execute the sequence
            results = await execute_sequence(config=config)
            
            print(f"✅ Completed variant '{variant.name}'")
            print(f"📁 Output saved to: {output_path}")
            
            return {
                'variant_name': variant.name,
                'status': 'success',
                'output_file': str(output_path),
                'sequence': final_sequence,
                'models': final_models,
                'steps_executed': len(results) if results else 0
            }
            
        except Exception as e:
            print(f"❌ Failed variant '{variant.name}': {e}")
            return {
                'variant_name': variant.name,
                'status': 'failed',
                'error': str(e),
                'sequence': variant.sequence,
                'models': variant.models or self.multi_config.default_models
            }

    async def execute_all(self) -> Dict[str, Any]:
        """Execute all variants in the multi-execution configuration."""
        if not self.multi_config:
            raise RuntimeError("Multi-execution configuration not loaded")

        print(f"\n🚀 Starting multi-execution: {self.multi_config.name}")
        if self.multi_config.description:
            print(f"📝 Description: {self.multi_config.description}")

        print(f"📊 Total variants: {len(self.multi_config.variants)}")
        print(f"⚡ Concurrent execution: {'Yes' if self.multi_config.concurrent else 'No'}")

        if self.multi_config.max_concurrent:
            print(f"🔢 Max concurrent: {self.multi_config.max_concurrent}")

        start_time = datetime.now()

        if self.multi_config.concurrent:
            # Execute variants concurrently
            if self.multi_config.max_concurrent:
                # Use semaphore to limit concurrency
                semaphore = asyncio.Semaphore(self.multi_config.max_concurrent)

                async def execute_with_semaphore(variant, index):
                    async with semaphore:
                        return await self.execute_variant(variant, index)

                tasks = [
                    execute_with_semaphore(variant, i)
                    for i, variant in enumerate(self.multi_config.variants)
                ]
            else:
                # No limit on concurrency
                tasks = [
                    self.execute_variant(variant, i)
                    for i, variant in enumerate(self.multi_config.variants)
                ]

            variant_results = await asyncio.gather(*tasks, return_exceptions=True)
        else:
            # Execute variants sequentially
            variant_results = []
            for i, variant in enumerate(self.multi_config.variants):
                result = await self.execute_variant(variant, i)
                variant_results.append(result)

        end_time = datetime.now()
        duration = end_time - start_time

        # Process results
        successful_variants = [r for r in variant_results if isinstance(r, dict) and r.get('status') == 'success']
        failed_variants = [r for r in variant_results if isinstance(r, dict) and r.get('status') == 'failed']
        exception_variants = [r for r in variant_results if isinstance(r, Exception)]

        # Summary
        print(f"\n📈 Multi-execution completed!")
        print(f"⏱️  Total duration: {duration}")
        print(f"✅ Successful variants: {len(successful_variants)}")
        print(f"❌ Failed variants: {len(failed_variants) + len(exception_variants)}")

        if failed_variants:
            print(f"\n❌ Failed variants:")
            for variant in failed_variants:
                print(f"  - {variant['variant_name']}: {variant['error']}")

        if exception_variants:
            print(f"\n💥 Variants with exceptions:")
            for i, exc in enumerate(exception_variants):
                print(f"  - Variant {i}: {exc}")

        return {
            'multi_execution_name': self.multi_config.name,
            'base_prompt': self.multi_config.base_prompt,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_seconds': duration.total_seconds(),
            'total_variants': len(self.multi_config.variants),
            'successful_variants': len(successful_variants),
            'failed_variants': len(failed_variants) + len(exception_variants),
            'variant_results': variant_results
        }


# =============================================================================
# CONVENIENCE FUNCTIONS
# =============================================================================

def create_template_exploration(base_prompt: str, template_series: List[str], name: str = "Template Exploration") -> MultiExecutionBuilder:
    """Create a multi-execution for exploring different templates with the same prompt."""
    builder = MultiExecutionBuilder(name, base_prompt)

    for i, template in enumerate(template_series, 1):
        variant_name = f"Template_{i:02d}_{template}"
        builder.add_variant(variant_name, template)

    return builder


def create_model_comparison(base_prompt: str, sequence: str, models: List[str], name: str = "Model Comparison") -> MultiExecutionBuilder:
    """Create a multi-execution for comparing different models with the same prompt and sequence."""
    builder = MultiExecutionBuilder(name, base_prompt)

    for model in models:
        variant_name = f"Model_{model.replace('/', '_').replace('-', '_')}"
        builder.add_variant(variant_name, sequence, models=[model])

    return builder


def create_parameter_sweep(base_prompt: str, sequence: str, parameter_configs: List[Dict[str, Any]], name: str = "Parameter Sweep") -> MultiExecutionBuilder:
    """Create a multi-execution for testing different parameter configurations."""
    builder = MultiExecutionBuilder(name, base_prompt)

    for i, config in enumerate(parameter_configs, 1):
        variant_name = f"Config_{i:02d}"
        if 'temperature' in config:
            variant_name += f"_temp{config['temperature']}"
        if 'max_tokens' in config:
            variant_name += f"_tokens{config['max_tokens']}"

        builder.add_variant(variant_name, sequence, **config)

    return builder


# =============================================================================
# CLI INTERFACE
# =============================================================================

async def main():
    """CLI entry point for multi-execution."""
    if len(sys.argv) != 2:
        print("Usage: python src/multi_execution.py <multi_config.json>")
        sys.exit(1)

    config_path = sys.argv[1]

    try:
        # Initialize and configure
        Config.configure_litellm()

        # Create and run multi-executor
        executor = MultiExecutor(config_path)
        executor.load_config()
        executor.load_catalog()

        # Execute all variants
        results = await executor.execute_all()

        # Save multi-execution results
        timestamp = datetime.now().strftime("%Y.%m.%d-kl.%H.%M.%S")
        results_file = Path("src/output") / f"multi_results--{timestamp}.json"

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        print(f"\n📁 Multi-execution results saved to: {results_file}")

    except Exception as e:
        print(f"❌ Multi-execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
