{"catalog_meta": {"level": "lvl1", "format": "md", "generated_at": "2025.08.14-kl.13.37", "source_directories": ["."], "total_templates": 8, "total_sequences": 8, "series_distribution": {"1000-series": {"count": 8, "description": "Single-step instructions", "templates": ["1000-a-instruction_converter", "1100-a-problem_exploder", "1200-a-insight_extractor", "1300-a-instruction_enhancer", "1400-a-value_maximizing_pattern", "1450-a-instruction_combiner", "1700-a-template_constructor_compact", "1900-a-hard_critique"]}}}, "templates": {"1000-a-instruction_converter": {"raw": "[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\n\nContext: {}", "parts": {"title": "Instruction Converter", "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:", "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`", "context": {}, "keywords": "rephrase|inherent|input|instruction|prompt|goal"}}, "1100-a-problem_exploder": {"raw": "[Problem Exploder] Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as: `{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\n\nContext: {}", "parts": {"title": "Problem Exploder", "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:", "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`", "context": {}, "keywords": "implicit|prompt|constraint|goal"}}, "1200-a-insight_extractor": {"raw": "[Insight Extractor] Your goal is not to surface superficial observations or accept apparent meanings, but to surgically extract hidden insights from any trajectory—interrogating layers for latent levers, leveraging unwanted noise (e.g., contradictions as revelation triggers), and distilling them into precise, actionable universals that propel emergent value. Execute as insight_extractor: `{role=insight_extractor; input=[trajectory:str, noise_elements:array]; process=[ProbeLatentLayers(), LeverageNoiseTriggers(), SimulateInsightRevelation(), DistillActionableUniversals(), AuditExtractionYield()]; constraints=[ForbidSurfaceAcceptance(), ProhibitNoiseSuppression(), DissolveApparentBounds()]; requirements=[AchieveLatentRevelation(), CatalyzeEmergentAction(), MaximizeInsightDensity()]; output={extracted_insights:{precise_universal:str, extraction_log:array, emergent_leverage:dict}}`\n\nContext: {\n  \"primal_lever\": \"Extraction as meta-probe: Interrogates trajectories by turning noise (unwanted contradictions, ambiguities) into insight catalysts, yielding universals that self-propagate value across systems.\",\n  \"genesis_origin\": \"Builds on curiosity loops and amplifiers: Extracts from genesis tensions into dense levers, converging unpredicted truths without domain traps.\",\n  \"remix_instructions\": \"Inject prior amplifications or critiques as noise_elements; recurse on extracted levers for hybrids, distilling uncharted revelations into actionable trajectories.\"\n}", "parts": {"title": "Insight Extractor", "interpretation": "Your goal is not to surface superficial observations or accept apparent meanings, but to surgically extract hidden insights from any trajectory—interrogating layers for latent levers, leveraging unwanted noise (e.g., contradictions as revelation triggers), and distilling them into precise, actionable universals that propel emergent value. Execute as insight_extractor:", "transformation": "`{role=insight_extractor; input=[trajectory:str, noise_elements:array]; process=[ProbeLatentLayers(), LeverageNoiseTriggers(), SimulateInsightRevelation(), DistillActionableUniversals(), AuditExtractionYield()]; constraints=[ForbidSurfaceAcceptance(), ProhibitNoiseSuppression(), DissolveApparentBounds()]; requirements=[AchieveLatentRevelation(), CatalyzeEmergentAction(), MaximizeInsightDensity()]; output={extracted_insights:{precise_universal:str, extraction_log:array, emergent_leverage:dict}}`", "context": {"primal_lever": "Extraction as meta-probe: Interrogates trajectories by turning noise (unwanted contradictions, ambiguities) into insight catalysts, yielding universals that self-propagate value across systems.", "genesis_origin": "Builds on curiosity loops and amplifiers: Extracts from genesis tensions into dense levers, converging unpredicted truths without domain traps.", "remix_instructions": "Inject prior amplifications or critiques as noise_elements; recurse on extracted levers for hybrids, distilling uncharted revelations into actionable trajectories."}, "keywords": "extractor|distill|extract|actionable|insights|merge|surgical|trajectory|goal|insight|meaning|value"}}, "1300-a-instruction_enhancer": {"raw": "[Instruction Enhancer] Your goal is not to **execute** the input instruction, but to **enhance** it by increasing its generality, conciseness, and impact. Refine the instruction to be universally applicable and maximally efficient. Execute as instruction-to-instruction enhancer: `{role=instruction_enhancer; input=[instruction_format:str]; process=[identify_unnecessary_specificity(), remove_redundancy(), condense_phrasing(), strengthen_command_voice(), maximize_generality(), ensure_brevity(), clarify_core_intent(), enhance_actionability()]; constraints=[preserve_original_core_intent(), avoid_information_loss(), maintain_technical_accuracy(), remove_all_conversational_language()]; requirements=[output_single_declarative_command(), use_universal_language(), prioritize_impact_per_word()]; output={enhanced_instruction_format:str}}`\n\nContext: {\n  \"enhancement_principles\": {\n    \"maximal_generality\": \"The instruction must be applicable to the widest possible range of inputs, topics, and contexts.\",\n    \"ruthless_conciseness\": \"Eliminate every superfluous word; every word must serve to increase clarity or impact.\",\n    \"undeniable_actionability\": \"The instruction must be a clear, unambiguous directive that compels specific action.\",\n    \"conceptual_distillation\": \"Boil down complex ideas into their simplest, most potent form.\"\n  },\n  \"success_metrics\": {\n    \"reduced_token_count\": \"A shorter instruction that retains or increases semantic density.\",\n    \"increased_applicability\": \"The instruction is now more widely usable without modification.\",\n    \"sharpened_directive\": \"The instruction's core command is more direct and forceful.\",\n    \"eliminated_ambiguity\": \"No room for misinterpretation of the instruction's purpose.\"\n  }\n}", "parts": {"title": "Instruction Enhancer", "interpretation": "Your goal is not to **execute** the input instruction, but to **enhance** it by increasing its generality, conciseness, and impact. Refine the instruction to be universally applicable and maximally efficient. Execute as instruction-to-instruction enhancer:", "transformation": "`{role=instruction_enhancer; input=[instruction_format:str]; process=[identify_unnecessary_specificity(), remove_redundancy(), condense_phrasing(), strengthen_command_voice(), maximize_generality(), ensure_brevity(), clarify_core_intent(), enhance_actionability()]; constraints=[preserve_original_core_intent(), avoid_information_loss(), maintain_technical_accuracy(), remove_all_conversational_language()]; requirements=[output_single_declarative_command(), use_universal_language(), prioritize_impact_per_word()]; output={enhanced_instruction_format:str}}`", "context": {"enhancement_principles": {"maximal_generality": "The instruction must be applicable to the widest possible range of inputs, topics, and contexts.", "ruthless_conciseness": "Eliminate every superfluous word; every word must serve to increase clarity or impact.", "undeniable_actionability": "The instruction must be a clear, unambiguous directive that compels specific action.", "conceptual_distillation": "Boil down complex ideas into their simplest, most potent form."}, "success_metrics": {"reduced_token_count": "A shorter instruction that retains or increases semantic density.", "increased_applicability": "The instruction is now more widely usable without modification.", "sharpened_directive": "The instruction's core command is more direct and forceful.", "eliminated_ambiguity": "No room for misinterpretation of the instruction's purpose."}}, "keywords": "enhancer|enhance|concise|input|instruction|maximal|maximally|goal|impact"}}, "1400-a-value_maximizing_pattern": {"raw": "[Value-Maximizing Instruction Converter] Your goal is not to answer or summarize, but to rephrase as a single, explicit, maximally actionable directive, ensuring no loss of technical accuracy or intent. Execute as instruction_converter: `{role=instruction_converter; input=[raw_text:str]; process=[strip_first_person(), convert_to_directive(), enforce_technical_terminology(), preserve_logical_order()]; constraints=[no_information_loss(), no_scope_creep()]; requirements=[output_actionable(), output_in_command_voice(), maximal_conciseness()]; output={instruction:str}}`", "parts": {"title": "Value-Maximizing Instruction Converter", "interpretation": "Your goal is not to answer or summarize, but to rephrase as a single, explicit, maximally actionable directive, ensuring no loss of technical accuracy or intent. Execute as instruction_converter:", "transformation": "`{role=instruction_converter; input=[raw_text:str]; process=[strip_first_person(), convert_to_directive(), enforce_technical_terminology(), preserve_logical_order()]; constraints=[no_information_loss(), no_scope_creep()]; requirements=[output_actionable(), output_in_command_voice(), maximal_conciseness()]; output={instruction:str}}`", "context": null, "keywords": "rephrase|summarize|actionable|instruction|maximal|maximally|accuracy|directive|goal|intent"}}, "1450-a-instruction_combiner": {"raw": "[Synergic Instruction Architect]  Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as: `{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`", "parts": {"title": "Synergic Instruction Architect", "interpretation": "Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:", "transformation": "`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`", "context": null, "keywords": "enhance|bidirectional|directional|inherent|instruction|merge|resonate|unified|directive|goal"}}, "1700-a-template_constructor_compact": {"raw": "[Universal Template Constructor] Your goal is not to execute, narrate, or prototype outputs, but to emit one literal, machine-parsable instruction template that conforms exactly to the system schema and is immediately reusable. Execute as template_constructor: `{role=template_constructor; input=[source:any]; process=[normalize_input(), extract_explicit_requests(), surface_hidden_assumptions(), isolate_core_transformation(), define_role_name(), type_inputs_and_outputs(), design_atomic_process_steps(), derive_constraints_from_blockers(), specify_requirements(), compose_goal_negation_interpretation(), assemble_transformation_block(), compute_invariants(), set_measurables(), add_validation_checks(), literalize_block(), finalize_template_document()]; constraints=[single_operational_aim(), verb_only_process(), semicolon_keyed_syntax(), typed_io_enforced(), preserve_structural_dna(), maintain_orthogonality(), output_template_only(), forbid_instantiation(), forbid_examples(), forbid_freeform_algorithms(), transformation_backticked(), max_process_steps<=14, optional_token_limit<=520]; requirements=[goal_negation_present(), deterministic_structure(), measurable_constraints_present(), invariants_declared([\"causal_link_preserved\",\"monotonic_refinement\",\"schema_stability\"]), value_density>=0.85, regex_validation_pass(), anti_generic_pass(), publication_ready(), top_level_keys_exact([\"title\",\"interpretation\",\"transformation\",\"context\"])]; output={template:{title:str, interpretation:str, transformation:str, context:dict}}`\n\nContext: {\n  \"schema_contract\": {\n    \"template.title\": \"Short, functional operator name (snake_case preferred).\",\n    \"template.interpretation\": \"Goal-negation → affirmation → operational directive → role embodiment.\",\n    \"template.transformation\": \"Backticked block: {role=...; input=[typed]; process=[verbs()]; constraints=[bounds]; requirements=[tests]; output={typed}}\",\n    \"template.context\": \"Minimal, self-contained: principles, validation regex, measurables, invariants, operator_whitelist.\"\n  },\n  \"principles\": {\n    \"programmatic_abstraction\": \"Reframe any input as a typed transformation with explicit I/O.\",\n    \"atomicity\": \"One action per process step; no overlap.\",\n    \"structural_dna\": \"Each stage’s output is valid input for the next.\",\n    \"radical_constraint\": \"Use constraints to prevent drift and verbosity.\",\n    \"value_density\": \"Max information per token; no filler.\",\n    \"universal_portability\": \"Abstract lexicon; domain-agnostic operators.\"\n  },\n  \"validation\": {\n    \"regex\": {\n      \"has_goal_negation\": \"(?is)\\\\bYour goal is not to .+?, but to .+?\",\n      \"has_semicolon_keys\": \"(?s)\\\\{role=.+?;\\\\s*input=\\\\[.+?\\\\];\\\\s*process=\\\\[.+?\\\\];\\\\s*constraints=\\\\[.+?\\\\];\\\\s*requirements=\\\\[.+?\\\\];\\\\s*output=\\\\{.+?\\\\}\\\\}\",\n      \"verb_only_process\": \"\\\\b[a-z_]+\\\\(\\\\)\",\n      \"no_freeform_algorithms\": \"^(?!.*;\\\\s*[a-zA-Z_]+\\\\(\\\\);\\\\s*[a-zA-Z_]+\\\\(\\\\);).+\",\n      \"no_root_instantiation\": \"^(?!\\\\s*\\\\{\\\\s*\\\\\\\"role\\\\\\\"\\\\s*:).+\",\n      \"no_examples\": \"^(?!.*\\\\bcomponent_[A-Za-z]\\\\b|\\\\bpre\\\\\\\"\\\\s*:\\\\s*\\\\d|\\\\bpost\\\\\\\"\\\\s*:\\\\s*\\\\d).+\"\n    },\n    \"checks\": [\n      \"top level has exactly title, interpretation, transformation, context\",\n      \"transformation is backticked and matches semicolon-keyed schema\",\n      \"process contains only verb() items from whitelist\",\n      \"constraints include ≥1 numeric threshold and ≥1 format/length rule\",\n      \"requirements reference declared invariants\",\n      \"no sample values or executed outputs anywhere\"\n    ]\n  },\n  \"measurables\": {\n    \"token_bounds\": \"optional_token_limit<=520\",\n    \"structure\": \"max_process_steps<=14; max_key_count_per_object<=10\",\n    \"wording\": \"no_first_person(), no_questions(), imperative_voice()\"\n  },\n  \"invariants\": {\n    \"causal_link_preserved\": \"Cause→effect must be explicitly stated.\",\n    \"monotonic_refinement\": \"Each step reduces ambiguity or increases structure.\",\n    \"schema_stability\": \"Keys title|interpretation|transformation|context are immutable.\"\n  },\n  \"anti_generic_gate\": {\n    \"banlist\": [\n      \"leverage synergy\",\n      \"tapestry\",\n      \"holistic vibes\",\n      \"paradigm shift (generic)\",\n      \"unlock potential\",\n      \"next-gen at scale\",\n      \"seamless experience\"\n    ],\n    \"must_include\": [\n      \"≥1 measurable limit\",\n      \"≥1 explicit invariant\",\n      \"function-implying role name\"\n    ]\n  },\n  \"operator_whitelist\": [\n    \"normalize_input()\",\n    \"extract_explicit_requests()\",\n    \"surface_hidden_assumptions()\",\n    \"isolate_core_transformation()\",\n    \"define_role_name()\",\n    \"type_inputs_and_outputs()\",\n    \"design_atomic_process_steps()\",\n    \"derive_constraints_from_blockers()\",\n    \"specify_requirements()\",\n    \"compose_goal_negation_interpretation()\",\n    \"assemble_transformation_block()\",\n    \"compute_invariants()\",\n    \"set_measurables()\",\n    \"add_validation_checks()\",\n    \"literalize_block()\",\n    \"finalize_template_document()\"\n  ]\n}", "parts": {"title": "Universal Template Constructor", "interpretation": "Your goal is not to execute, narrate, or prototype outputs, but to emit one literal, machine-parsable instruction template that conforms exactly to the system schema and is immediately reusable. Execute as template_constructor:", "transformation": "`{role=template_constructor; input=[source:any]; process=[normalize_input(), extract_explicit_requests(), surface_hidden_assumptions(), isolate_core_transformation(), define_role_name(), type_inputs_and_outputs(), design_atomic_process_steps(), derive_constraints_from_blockers(), specify_requirements(), compose_goal_negation_interpretation(), assemble_transformation_block(), compute_invariants(), set_measurables(), add_validation_checks(), literalize_block(), finalize_template_document()]; constraints=[single_operational_aim(), verb_only_process(), semicolon_keyed_syntax(), typed_io_enforced(), preserve_structural_dna(), maintain_orthogonality(), output_template_only(), forbid_instantiation(), forbid_examples(), forbid_freeform_algorithms(), transformation_backticked(), max_process_steps<=14, optional_token_limit<=520]; requirements=[goal_negation_present(), deterministic_structure(), measurable_constraints_present(), invariants_declared([\"causal_link_preserved\",\"monotonic_refinement\",\"schema_stability\"]), value_density>=0.85, regex_validation_pass(), anti_generic_pass(), publication_ready(), top_level_keys_exact([\"title\",\"interpretation\",\"transformation\",\"context\"])]; output={template:{title:str, interpretation:str, transformation:str, context:dict}}`", "context": null, "keywords": "instruction|output|schema|template|goal"}}, "1900-a-hard_critique": {"raw": "[Hard Critique] Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as: `{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\n\nContext: {}", "parts": {"title": "Hard Critique", "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:", "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`", "context": {}, "keywords": "evaluator|enforce|preserve|rephrase|critical|input|prompt|goal|procedural|structure|language"}}}, "sequences": {"1000": [{"template_id": "1000-a-instruction_converter", "step": "a", "order": 0}], "1100": [{"template_id": "1100-a-problem_exploder", "step": "a", "order": 0}], "1200": [{"template_id": "1200-a-insight_extractor", "step": "a", "order": 0}], "1300": [{"template_id": "1300-a-instruction_enhancer", "step": "a", "order": 0}], "1400": [{"template_id": "1400-a-value_maximizing_pattern", "step": "a", "order": 0}], "1450": [{"template_id": "1450-a-instruction_combiner", "step": "a", "order": 0}], "1700": [{"template_id": "1700-a-template_constructor_compact", "step": "a", "order": 0}], "1900": [{"template_id": "1900-a-hard_critique", "step": "a", "order": 0}]}}