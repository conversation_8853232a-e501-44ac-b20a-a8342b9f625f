# Multi-Execution Feature

## Overview

The multi-execution feature allows you to execute the same prompt through multiple different instruction templates in a single command, maintaining the elegant simplicity of your existing system.

## Usage

Simply use the `[MULTI:seq1,seq2,seq3]` syntax in your prompt, just like the existing `[SEQ:...]` and `[MODEL:...]` patterns.

### Basic Syntax

```bash
# Execute the same prompt through 3 different templates
"[MULTI:1000,1001,1002] Your prompt here"

# Combine with model specification
"[MODEL:gpt-3.5-turbo] [MULTI:1000,2000:a-b,3000:a-c] Your prompt here"

# Mix different sequence types
"[MULTI:1000,1404|1405,3100:a-c] Your prompt here"
```

### Examples

```bash
# Compare single-step vs multi-step approaches
python src/main.py --prompt "[MULTI:1000,2000:a-b,3000:a-c] Analyze the philosophical implications of AI"

# Test different compression techniques
python src/main.py --prompt "[MODEL:gpt-4o] [MULTI:1400,1401,1402] Compress this complex technical document"

# Explore enhancement variations
python src/main.py --prompt "[MULTI:1300,1301,1302,1303] Enhance this basic instruction"
```

## How It Works

1. **Parsing**: The system detects `[MULTI:...]` in your prompt
2. **Extraction**: Extracts the comma-separated sequence list
3. **Execution**: Runs each sequence independently with the same base prompt
4. **Output**: Creates separate output files for each execution with numbered suffixes

## Output Files

Each execution creates a separate output file:
- `history--timestamp--sequence-1000--model.json`
- `history--timestamp--sequence-1001--model.json`
- `history--timestamp--sequence-1002--model.json`

## Integration with Existing Features

The multi-execution feature seamlessly integrates with all existing functionality:

- **Model Selection**: `[MODEL:...]` applies to all executions
- **CLI Arguments**: All flags (`--temperature`, `--max-tokens`, etc.) apply to each execution
- **Output Options**: `--minified-output`, `--no-inputs`, etc. work as expected
- **Performance Monitoring**: Each execution is timed separately

## Default Prompt Example

The default prompt has been updated to demonstrate multi-execution:

```python
default_prompt = "[MODEL:gpt-3.5-turbo] [MULTI:1000,1001,1002] Apply only the highest-value components and produce the most elegant interpretation possible."
```

## Benefits

1. **Elegant**: Uses existing prompt parsing patterns
2. **Minimal**: No new CLI arguments or configuration files needed
3. **Consistent**: Follows the same syntax conventions as `[SEQ:...]` and `[MODEL:...]`
4. **Powerful**: Enables complex template comparisons with a single command
5. **Maintainable**: Leverages existing architecture without adding complexity

## Use Cases

- **Template Comparison**: Test the same prompt across different instruction templates
- **Depth Analysis**: Compare single-step vs multi-step processing
- **Enhancement Exploration**: Try different enhancement approaches
- **Model Comparison**: Combined with `[MODEL:...]` for comprehensive testing
- **Research**: Systematic exploration of template effectiveness

This solution respects the inherent elegance of your system while providing the exact functionality you requested.
