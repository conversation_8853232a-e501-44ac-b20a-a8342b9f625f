@echo off
:: =============================================================================
:: Shared Batch Utilities for AI Systems
:: Consolidates common setup and dependency management
:: =============================================================================

:: Function: Check UV Installation
:check_uv
where uv >nul 2>&1
if errorlevel 1 (
    echo Error: uv is not installed or not in PATH
    echo Please install uv: https://docs.astral.sh/uv/getting-started/installation/
    pause
    exit /b 1
)
goto :eof

:: Function: Sync Dependencies
:sync_dependencies
echo Syncing dependencies with uv...
uv sync
if errorlevel 1 (
    echo Error: Failed to sync dependencies
    pause
    exit /b 1
)
goto :eof

:: Function: Install Development Package
:install_dev_package
echo Installing package in development mode...
uv pip install -e .
if errorlevel 1 (
    echo Warning: Failed to install package in development mode
    echo Templates may not work properly
)
goto :eof

:: Function: Setup Environment (combines all setup steps)
:setup_environment
cd /d "%~dp0"
call :check_uv
call :sync_dependencies
call :install_dev_package
goto :eof

:: Function: Pause if no arguments provided
:pause_if_no_args
if "%~1"=="" (
    echo.
    pause
)
goto :eof
