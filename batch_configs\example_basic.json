{"version": "1.0", "name": "System Self-Analysis Batch", "description": "Comprehensive self-analysis using the system's own templates", "defaults": {"chain_mode": true, "show_inputs": false, "show_system_instructions": false, "show_responses": true, "show_performance": true, "minified_output": false}, "concurrent": true, "max_concurrent": 3, "jobs": [{"name": "Architecture Analysis", "prompt": "AI Systems template-driven LLM orchestration engine with main.py orchestrator, processor.py catalog management, SequenceManager for resolution, TemplateCatalog for multi-level management, LiteLLM multi-provider abstraction, performance monitoring, streaming responses, and instruction-as-data philosophy", "sequence": "1750", "models": ["gpt-3.5-turbo"], "output_prefix": "architecture_analysis"}, {"name": "Performance Optimization", "prompt": "Performance metrics: catalog_regeneration=0.001s, model_execution=2.463s average with variance, sequence_execution=7.403s total. Model execution dominates 99.8% of execution time. System shows good catalog performance but needs optimization for model calls and streaming responses.", "sequence": "1800", "models": ["gpt-3.5-turbo"], "output_prefix": "performance_optimization"}, {"name": "Template Generation", "prompt": "Create a template for real-time system monitoring that tracks execution metrics, identifies performance bottlenecks, and suggests dynamic optimizations during runtime. The template should output monitoring dashboards with actionable insights.", "sequence": "1700", "models": ["gpt-3.5-turbo"], "output_prefix": "monitoring_template"}]}