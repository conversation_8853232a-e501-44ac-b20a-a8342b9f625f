{"version": "1.0", "name": "Template Exploration - Same Prompt Multiple Templates", "description": "Execute the same prompt through different instruction templates to compare approaches", "base_prompt": "[MODEL:gpt-5-chat-latest] [SEQ:1000] Apply only the highest-value components and produce the most elegant interpretation possible.", "default_models": ["gpt-3.5-turbo"], "defaults": {"chain_mode": true, "show_inputs": false, "show_system_instructions": false, "show_responses": true, "minified_output": false}, "concurrent": true, "max_concurrent": 3, "variants": [{"name": "Single_Step_Basic", "sequence": "1000", "output_suffix": "single_basic"}, {"name": "Single_Step_Enhanced", "sequence": "1001", "output_suffix": "single_enhanced"}, {"name": "Double_Step_Analysis", "sequence": "2000:a-b", "output_suffix": "double_analysis"}, {"name": "Triple_Step_Deep", "sequence": "3000:a-c", "output_suffix": "triple_deep"}, {"name": "Compression_Focus", "sequence": "1400|1401", "output_suffix": "compression"}, {"name": "Enhancement_Chain", "sequence": "1300|1301|1302", "output_suffix": "enhancement"}, {"name": "Critical_Analysis", "sequence": "1900|1901", "output_suffix": "critical"}]}