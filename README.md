# AI Systems
Template-driven multi-LLM instruction sequence executor.

## Setup
```bash
uv sync && uv pip install -e .
```

## Usage
```bash
# List available sequences
run.bat --list-sequences

# Single sequence
run.bat --sequence 1000 --prompt "Your text"

# Chained sequences
run.bat --sequence "1000|1300" --prompt "Your text"

# Test scenarios
run.bat --test-scenario simple

# Batch execution
run.bat --batch-file batch_configs/example_basic.json
```

## Series Organization

### Step Patterns (X000)
- **1000-1999**: Single-step instructions (pattern: `a`)
- **2000-2999**: Double-step instructions (pattern: `a-b`)
- **3000-3999**: Triple-step instructions (pattern: `a-c`)
- **4000-4999**: Quad-step instructions (pattern: `a-d`)

### Template Types (0X00)
- **000**: Rephrasers (instruction rephrasing and conversion)
- **100**: Expanders (problem decomposition and expansion)
- **200**: Clarifiers (clarification and refinement)
- **300**: Analyzers (analysis and evaluation)

### Examples
- `1000`: Single-step rephraser
- `1100`: Single-step expander
- `2000`: Double-step rephraser
- `2100`: Double-step expander

## Sequences
- Single: `1000`
- Chained: `1000|1300`
- Range: `1000:a`
- Keyword: `keyword:distill`

## Config
Set API keys: `OPENAI_API_KEY`, `ANTHROPIC_API_KEY`, `OPENROUTER_API_KEY`

## Architecture
- `main.py` → CLI orchestrator
- `processor.py` → Template catalog
- `templates/` → Python generators
- `generated/` → Compiled templates
- `output/` → JSON results

Templates: Python → Markdown → JSON → Runtime execution
