# Batch Configs

## Usage
```bash
run_batch.bat example_basic.json
```

## Schema
```json
{
  "concurrent": true,
  "max_concurrent": 2,
  "jobs": [
    {
      "name": "job_name",
      "prompt": "Your prompt",
      "sequence": "1000|1300",
      "models": ["gpt-4.1"]
    }
  ]
}
```

## Examples
- `example_basic.json` → Concurrent execution
- `example_advanced.json` → Multi-model with custom params
- `example_sequential.json` → Sequential execution
