#!/usr/bin/env python3

from pathlib import Path

# Import BaseGenerator from the processor module
from processor import BaseGenerator

TEMPLATES = {

  "1700-a-template_constructor_compact": {
    "title": "Universal Template Constructor",
    "interpretation": "Your goal is not to execute, narrate, or prototype outputs, but to emit one literal, machine-parsable instruction template that conforms exactly to the system schema and is immediately reusable. Execute as template_constructor:",
    "transformation": "`{role=template_constructor; input=[source:any]; process=[normalize_input(), extract_explicit_requests(), surface_hidden_assumptions(), isolate_core_transformation(), define_role_name(), type_inputs_and_outputs(), design_atomic_process_steps(), derive_constraints_from_blockers(), specify_requirements(), compose_goal_negation_interpretation(), assemble_transformation_block(), compute_invariants(), set_measurables(), add_validation_checks(), literalize_block(), finalize_template_document()]; constraints=[single_operational_aim(), verb_only_process(), semicolon_keyed_syntax(), typed_io_enforced(), preserve_structural_dna(), maintain_orthogonality(), output_template_only(), forbid_instantiation(), forbid_examples(), forbid_freeform_algorithms(), transformation_backticked(), max_process_steps<=14, optional_token_limit<=520]; requirements=[goal_negation_present(), deterministic_structure(), measurable_constraints_present(), invariants_declared([\"causal_link_preserved\",\"monotonic_refinement\",\"schema_stability\"]), value_density>=0.85, regex_validation_pass(), anti_generic_pass(), publication_ready(), top_level_keys_exact([\"title\",\"interpretation\",\"transformation\",\"context\"])]; output={template:{title:str, interpretation:str, transformation:str, context:dict}}`",
    "context": {
      "schema_contract": {
        "template.title": "Short, functional operator name (snake_case preferred).",
        "template.interpretation": "Goal-negation → affirmation → operational directive → role embodiment.",
        "template.transformation": "Backticked block: {role=...; input=[typed]; process=[verbs()]; constraints=[bounds]; requirements=[tests]; output={typed}}",
        "template.context": "Minimal, self-contained: principles, validation regex, measurables, invariants, operator_whitelist."
      },
      "principles": {
        "programmatic_abstraction": "Reframe any input as a typed transformation with explicit I/O.",
        "atomicity": "One action per process step; no overlap.",
        "structural_dna": "Each stage’s output is valid input for the next.",
        "radical_constraint": "Use constraints to prevent drift and verbosity.",
        "value_density": "Max information per token; no filler.",
        "universal_portability": "Abstract lexicon; domain-agnostic operators."
      },
      "validation": {
        "regex": {
          "has_goal_negation": "(?is)\\bYour goal is not to .+?, but to .+?",
          "has_semicolon_keys": "(?s)\\{role=.+?;\\s*input=\\[.+?\\];\\s*process=\\[.+?\\];\\s*constraints=\\[.+?\\];\\s*requirements=\\[.+?\\];\\s*output=\\{.+?\\}\\}",
          "verb_only_process": "\\b[a-z_]+\\(\\)",
          "no_freeform_algorithms": "^(?!.*;\\s*[a-zA-Z_]+\\(\\);\\s*[a-zA-Z_]+\\(\\);).+",
          "no_root_instantiation": "^(?!\\s*\\{\\s*\\\"role\\\"\\s*:).+",
          "no_examples": "^(?!.*\\bcomponent_[A-Za-z]\\b|\\bpre\\\"\\s*:\\s*\\d|\\bpost\\\"\\s*:\\s*\\d).+"
        },
        "checks": [
          "top level has exactly title, interpretation, transformation, context",
          "transformation is backticked and matches semicolon-keyed schema",
          "process contains only verb() items from whitelist",
          "constraints include ≥1 numeric threshold and ≥1 format/length rule",
          "requirements reference declared invariants",
          "no sample values or executed outputs anywhere"
        ]
      },
      "measurables": {
        "token_bounds": "optional_token_limit<=520",
        "structure": "max_process_steps<=14; max_key_count_per_object<=10",
        "wording": "no_first_person(), no_questions(), imperative_voice()"
      },
      "invariants": {
        "causal_link_preserved": "Cause→effect must be explicitly stated.",
        "monotonic_refinement": "Each step reduces ambiguity or increases structure.",
        "schema_stability": "Keys title|interpretation|transformation|context are immutable."
      },
      "anti_generic_gate": {
        "banlist": [
          "leverage synergy",
          "tapestry",
          "holistic vibes",
          "paradigm shift (generic)",
          "unlock potential",
          "next-gen at scale",
          "seamless experience"
        ],
        "must_include": [
          "≥1 measurable limit",
          "≥1 explicit invariant",
          "function-implying role name"
        ]
      },
      "operator_whitelist": [
        "normalize_input()",
        "extract_explicit_requests()",
        "surface_hidden_assumptions()",
        "isolate_core_transformation()",
        "define_role_name()",
        "type_inputs_and_outputs()",
        "design_atomic_process_steps()",
        "derive_constraints_from_blockers()",
        "specify_requirements()",
        "compose_goal_negation_interpretation()",
        "assemble_transformation_block()",
        "compute_invariants()",
        "set_measurables()",
        "add_validation_checks()",
        "literalize_block()",
        "finalize_template_document()"
      ]
    }
  },

  # # 1750: System Architecture Analyzer
  # "1750-a-system_analyzer": {
  #   "title": "System Architecture Analyzer",
  #   "interpretation": "Your goal is not to **execute** the system analysis, but to **transform** the codebase description into a structured architectural assessment with improvement recommendations. Execute as system-analyzer:",
  #   "transformation": "`{role=system_analyzer; input=[codebase_description:str]; process=[identify_core_components(), map_data_flows(), analyze_integration_patterns(), detect_architectural_strengths(), identify_improvement_opportunities(), assess_scalability_potential(), evaluate_maintainability_factors()]; constraints=[focus_on_structural_patterns(), avoid_implementation_details(), maintain_objective_assessment()]; requirements=[provide_component_analysis(), suggest_architectural_enhancements(), identify_potential_bottlenecks(), recommend_optimization_strategies()]; output={architectural_analysis:dict}}`",
  #   "context": {
  #     "analysis_dimensions": {
  #       "component_cohesion": "How well components serve single responsibilities",
  #       "coupling_analysis": "Dependencies and interaction patterns between modules",
  #       "data_flow_efficiency": "How data moves through the system architecture",
  #       "extensibility_assessment": "Ease of adding new functionality without disruption",
  #       "performance_considerations": "Potential bottlenecks and optimization opportunities"
  #     },
  #     "improvement_categories": {
  #       "architectural_patterns": "Design pattern applications and improvements",
  #       "code_organization": "Structure and modularity enhancements",
  #       "integration_optimization": "Component interaction improvements",
  #       "scalability_enhancements": "Growth and performance optimizations"
  #     }
  #   }
  # },

  # # 1800: Performance Optimizer
  # "1800-a-performance_optimizer": {
  #   "title": "Performance Optimizer",
  #   "interpretation": "Your goal is not to **execute** performance optimizations, but to **transform** performance metrics into actionable optimization recommendations with specific implementation strategies. Execute as performance-optimizer:",
  #   "transformation": "`{role=performance_optimizer; input=[performance_metrics:dict]; process=[analyze_execution_times(), identify_bottlenecks(), calculate_optimization_potential(), prioritize_improvements(), design_implementation_strategies(), estimate_performance_gains()]; constraints=[focus_on_measurable_improvements(), provide_specific_targets(), maintain_system_stability()]; requirements=[quantify_current_performance(), suggest_concrete_optimizations(), estimate_improvement_percentages(), provide_implementation_roadmap()]; output={optimization_plan:dict}}`",
  #   "context": {
  #     "optimization_categories": {
  #       "async_execution": "Parallel processing and concurrency improvements",
  #       "caching_strategies": "Template and catalog caching mechanisms",
  #       "streaming_optimization": "Response streaming and buffer management",
  #       "resource_pooling": "Connection and model instance pooling",
  #       "batch_processing": "Efficient batch execution patterns"
  #     },
  #     "performance_targets": {
  #       "catalog_regeneration": "< 0.1s for typical catalogs",
  #       "model_execution": "< 1.5s average response time",
  #       "sequence_execution": "< 2.0s for simple chains",
  #       "memory_usage": "< 100MB baseline consumption",
  #       "concurrent_requests": "> 10 simultaneous sequences"
  #     },
  #     "implementation_strategies": {
  #       "lazy_loading": "Load templates and catalogs on-demand",
  #       "connection_pooling": "Reuse HTTP connections for model calls",
  #       "response_caching": "Cache frequent template transformations",
  #       "async_optimization": "Improve asyncio event loop efficiency",
  #       "batch_aggregation": "Group multiple requests for efficiency"
  #     }
  #   }
  # }

}

def main():
    """Main execution function."""
    generator = BaseGenerator()
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
