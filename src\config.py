#!/usr/bin/env python3
"""
Centralized configuration for AI Systems
Contains all constants, defaults, and configuration settings
"""

import os

# =============================================================================
# PATH CONFIGURATION
# =============================================================================

def get_script_dir():
    """Get current script directory."""
    return os.path.dirname(os.path.abspath(__file__))

# Default paths
SCRIPT_DIR = get_script_dir()
DEFAULT_OUTPUT_DIR = os.path.join(SCRIPT_DIR, "output")

# =============================================================================
# MODEL CONFIGURATION
# =============================================================================

# Model Registry (maps user-friendly names to actual LiteLLM model IDs)
MODEL_REGISTRY = {
    # OpenAI
    "gpt-3.5-turbo": "gpt-3.5-turbo",
    "gpt-3.5-turbo-instruct": "gpt-3.5-turbo-instruct",
    "gpt-4o": "gpt-4o",
    "gpt-4": "gpt-4",
    "gpt-4-turbo": "gpt-4-turbo",
    "gpt-4.1": "gpt-4.1",
    "o3-mini": "o3-mini",
    "gpt-5": "gpt-5",
    "gpt-5-mini": "gpt-5-mini",
    "gpt-5-nano": "gpt-5-nano",
    "gpt-5-chat": "gpt-3.5-turbo",
    "gpt-5-chat-latest": "gpt-3.5-turbo",
    # Anthropic
    "claude-3-opus": "anthropic/claude-3-opus-20240229",
    "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",
    "claude-3-haiku": "anthropic/claude-3-haiku-20240307",
    "claude-3.7-sonnet": "openrouter/anthropic/claude-3.7-sonnet:beta",
    "claude-opus-4-20250514": "claude-opus-4-20250514",
    "claude-sonnet-4-20250514": "claude-sonnet-4-20250514",
    # Google
    "gemini-pro": "gemini/gemini-1.5-pro",
    "gemini-flash": "gemini/gemini-1.5-flash-latest",
    "gemini-2-flash": "gemini/gemini-2.0-flash",
    "gemini-2.5-pro": "gemini/gemini-2.5-pro-preview-03-25",
    # Deepseek
    "deepseek-reasoner": "deepseek/deepseek-reasoner",
    "deepseek-coder": "deepseek/deepseek-coder",
    "deepseek-chat": "deepseek/deepseek-chat",
    # Others
    "kimi-k2-instruct": "groq/moonshotai/kimi-k2-instruct",
}

# Provider Selection
PROVIDERS = {
    "anthropic": "anthropic",
    "deepseek": "deepseek",
    "google": "google",
    "openai": "openai"
}

# Default provider
DEFAULT_PROVIDER = "openai"

# Note: MODEL_CONFIG moved to main.py Config class since it's only used there

# =============================================================================
# SEQUENCE CONFIGURATION
# =============================================================================

# Default sequences for different purposes
DEFAULT_SEQUENCES = {
    "general": "1000",
    "analysis": "1750",
    "enhancement": "1300",
    "critique": "1900"
}

# Series configuration based on step patterns
SERIES_CONFIG = {
    0: {
        "name": "Special Instructions",
        "range": (0, 999),
        "pattern": "a",
        "description": "Special instructions (single step)"
    },
    1000: {
        "name": "Single-Step Instructions",
        "range": (1000, 1999),
        "pattern": "a",
        "description": "Single-step instructions (just a)"
    },
    2000: {
        "name": "Double-Step Instructions",
        "range": (2000, 2999),
        "pattern": "a-b",
        "description": "Double-step instructions (a-b)"
    },
    3000: {
        "name": "Triple-Step Instructions",
        "range": (3000, 3999),
        "pattern": "a-c",
        "description": "Triple-step instructions (a-c)"
    },
    4000: {
        "name": "Quad-Step Instructions",
        "range": (4000, 4999),
        "pattern": "a-d",
        "description": "Quad-step instructions (a-d)"
    }
}

# =============================================================================
# EXECUTION DEFAULTS
# =============================================================================

# Default execution settings
EXECUTION_DEFAULTS = {
    "chain_mode": True,
    "show_inputs": False,
    "show_system_instructions": False,
    "show_responses": True,
    "show_performance": False,
    "minified_output": False,
    "temperature": None,
    "max_tokens": None
}

# LiteLLM configuration
LITELLM_CONFIG = {
    "drop_params": True,
    "num_retries": 3,
    "request_timeout": 120,
    "set_verbose": False,
    "callbacks": []
}

# =============================================================================
# FILE NAMING PATTERNS
# =============================================================================

# Output filename patterns
FILENAME_PATTERNS = {
    "history": "history--{timestamp}--{sequence_id}--{model_tag}.json",
    "timestamp_format": "%Y.%m.%d-kl.%H.%M.%S"
}

# File sanitization replacements
FILENAME_SANITIZATION = {
    "|": "+", ":": "-", "?": "_", "*": "_",
    "<": "_", ">": "_", "\"": "_", "/": "_", "\\": "_"
}

# =============================================================================
# INTERACTIVE CLI CONFIGURATION
# =============================================================================

# Interactive CLI specific settings
INTERACTIVE_CONFIG = {
    "recent_executions_limit": 10,
    "generator_preview_limit": 5,
    "recommended_sequences": ["3000", "3031", "3100"],
    "recommended_models": ["gpt-4o", "claude-3-sonnet"]
}

# =============================================================================
# TEMPLATE SYSTEM CONFIGURATION
# =============================================================================

# Template catalog settings
CATALOG_CONFIG = {
    "format": "md",
    "source_directories": ["generated"],
    "generator_directories": []
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def get_series_name(series_num):
    """Get descriptive name for a series."""
    config = SERIES_CONFIG.get(series_num)
    if config:
        return f"Series {series_num}: {config['name']} ({config['range'][0]}-{config['range'][1]})"
    return f"Series {series_num}"

def get_series_from_id(seq_id):
    """Extract series number from sequence ID."""
    try:
        num = int(seq_id)
        for series, config in SERIES_CONFIG.items():
            start, end = config["range"]
            if start <= num <= end:
                return series
        return 0
    except (ValueError, TypeError):
        return 0

# Backward compatibility aliases (deprecated)
def get_stage_name(stage_num):
    """Deprecated: Use get_series_name instead."""
    return get_series_name(stage_num)

def get_stage_from_id(seq_id):
    """Deprecated: Use get_series_from_id instead."""
    return get_series_from_id(seq_id)

def get_default_model(provider=None):
    """Get default model - simplified for shared use."""
    # Simple default for shared usage (interactive CLI, etc.)
    return "gpt-3.5-turbo"
