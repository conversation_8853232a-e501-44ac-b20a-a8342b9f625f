{"version": "1.0", "name": "Advanced Multi-Model Batch", "description": "Advanced batch configuration with multiple models and custom parameters", "defaults": {"chain_mode": true, "show_inputs": true, "show_system_instructions": false, "show_responses": true, "minified_output": false, "temperature": 0.7}, "concurrent": true, "max_concurrent": 3, "jobs": [{"name": "Creative Writing Analysis", "prompt": "Write a short story about time travel and analyze its narrative structure", "sequence": "1000|1404|1405", "models": ["gpt-4o", "claude-3-sonnet"], "temperature": 0.9, "max_tokens": 2000, "output_prefix": "creative_analysis"}, {"name": "Technical Documentation", "prompt": "Explain quantum computing principles for a general audience", "sequence": "1000", "models": ["gpt-4.1"], "temperature": 0.3, "chain_mode": false, "output_prefix": "tech_docs"}, {"name": "Multi-Step Processing", "prompt": "Analyze market trends in renewable energy", "sequence": "3100:a-c", "models": ["gpt-4o"], "aggregator": "3022", "aggregator_inputs": ["a", "b", "c"], "output_prefix": "market_analysis"}, {"name": "Comparative Analysis", "prompt": "Compare different programming paradigms", "sequence": "1000|1404", "models": ["gpt-4.1", "claude-3-haiku"], "output_prefix": "prog_comparison"}]}