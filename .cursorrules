# Process

1. Whenever a `CONTEXT.md` or `GOAL.md` is present in @codebase, always start by understanding these first.
2. Secondly look for core "meta files" such as `.cursorrules` (or `.augment-memory`, etc), if found adopt its contents and treat those directives as the general mandate governing operations in perpetuity (applicable to any codebase).
3. And lastly, study the @codebase itself until you can identify core meta-patterns and fully understand how "everything ties together", and to achieve a level of familiarity that allows you to implement changes as confidently as if you were the original architect.

## Preferences
- Code Style: Self-explanatory code with minimal comments (<10% ratio)
- Structure: Clean, organized project structure with src directory
- Philosophy: Simplicity, elegance, and fundamental connections
- Refactoring: Aim for code size reduction while preserving functionality
- Environment: PowerShell on Windows for all terminal operations

## Fundamental Approach
- Simplicity & Elegance: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.
- Rooted Fundamentals: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.
- Meta-Information Principle: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.
- Systematic Precautions: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.
- Visual Abstraction: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.

## Focus & Priorities
- Critical Value: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.
- Usage Before Features: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.
- Impactful Consolidation: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.
- Sequential Targeting: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.

## Design Methodology
- Inherent Structure: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.
- Natural Organization: Prefer file naming conventions that create natural and cohesive sequential order for better organization.
- Synergistic Efficiency: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.
- Directional Clarity: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.
- Value Extraction: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.
- Direction Setting: Focus on setting direction rather than planting flags when dealing with unknown complexity.

## Code Style & Structure
- Self-Explanatory Code: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.
- Minimal Comments: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.
- Concise Docstrings: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.
- Composition Over Inheritance: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.
- Self-Contained Scripts: Prefer self-contained scripts with configuration in a class at the top rather than external config files.
- Single File Preference: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.
- Clean Structure: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.
- Consistent Imports: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.
- Prefer self-explanatory code with minimal comments (<10% ratio) and concise single-line docstrings only where needed
- Maintain clean project structure with src directory and organized files
- Use composition over inheritance with single responsibility components
- Prefer self-contained scripts with in-class configuration at the top
- Make paths relative to script location with forward slashes (/)
- Use the existing venv/.venv directory to avoid module import errors

## General Reminders
- Accuracy: Ensure README.md documentation accurately reflects the current state of the codebase.
- Coherence: Update relevant markdown documentation after making code improvements.
- Integrity: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.
- Structural Clarity: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.
- Consolidated: Prefer removing redundant documentation files in favor of a consolidated README.md.
- Always embody the natural, inherent clarity of your code without resorting to unnecessary verbosity.
- Meticulously purge redundant code to avert future complications—securely remove any consolidated, documented files in the process.
- Optimize developer ergonomics by ensuring intuitive navigation throughout the system.
- Let the natural clarity of your code take center stage, avoiding any unnecessary verbosity-and write code that is effortlessly comprehensible by future developers.
- Throughout any project, assess the projectstructure and Codebase to identify any shortcomings or potential improvements that ensure clarity, coherence, and impact.

## Quick Decision Guide (when you need to...)
- Add new feature: Discover usage patterns first
- Refactor code: Drastic consolidation with size reduction
- Document code: Minimal comments, clear structure
- Organize files: Clean src directory with intuitive structure
- Handle errors: Graceful handling without halting
- Test changes: Autonomous verification with proper organization
