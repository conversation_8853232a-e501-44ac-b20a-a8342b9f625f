#!/usr/bin/env python3
"""
Interactive CLI for AI Systems - Enhanced user experience
Reuses all existing components without modification to main.py
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime

# Import all existing components - zero changes needed to main.py
from main import (
    TemplateCatalog, SequenceManager, Config,
    ExecutorConfig, execute_sequence,
    PromptParser, PathUtils
)

# Import display utilities
from display import print_json, print_sequences, print_params, print_step, print_response

# Import centralized configuration
try:
    # Try relative import first (when running from src/)
    from config import (
        get_stage_name, get_stage_from_id, INTERACTIVE_CONFIG,
        DEFAULT_SEQUENCES, MODEL_REGISTRY, get_default_model, EXECUTION_DEFAULTS
    )
except ImportError:
    # Fallback to absolute import (when running from project root)
    from src.config import (
        get_stage_name, get_stage_from_id, INTERACTIVE_CONFIG,
        DEFAULT_SEQUENCES, MODEL_REGISTRY, get_default_model, EXECUTION_DEFAULTS
    )

class InteractiveCLI:
    """Enhanced interactive CLI that wraps existing functionality."""

    def __init__(self):
        self.catalog = None
        self.config_loaded = False
        self.recent_sequences = []

    async def run(self):
        """Main interactive loop."""
        print("🚀 AI Systems - Interactive Mode")
        print("=" * 50)
        print("Enhanced CLI for streamlined sequence building and execution")

        # Initialize (reuse existing setup)
        await self.initialize()

        while True:
            try:
                choice = await self.show_main_menu()
                if choice == 'quit':
                    break
                await self.handle_choice(choice)
            except KeyboardInterrupt:
                print("\n\nGoodbye! 👋")
                break

    async def initialize(self):
        """Initialize using existing components."""
        if not self.config_loaded:
            print("🔧 Initializing system...")
            Config.configure_litellm()

            try:
                self.catalog = TemplateCatalog.regenerate_catalog(force=False)
                if self.catalog and self.catalog.get("templates"):
                    template_count = len(self.catalog.get("templates", {}))
                    sequence_count = len(self.catalog.get("sequences", {}))
                    print(f"✅ System ready! Found {template_count} templates, {sequence_count} sequences")
                else:
                    print("⚠️  System ready, but catalog is empty or missing")
                    print("💡 You may need to generate markdown files from Python generators")
            except Exception as e:
                print(f"⚠️  System ready, but catalog loading failed: {e}")
                print("💡 You may need to generate markdown files from Python generators")
                self.catalog = None

            self.config_loaded = True

    async def show_main_menu(self):
        """Display main menu and get user choice."""
        print("\n" + "=" * 50)
        print("📋 Main Menu")
        print("=" * 50)

        # Show different options based on catalog availability
        if self.catalog and self.catalog.get("templates"):
            print("1. 🔍 Browse & Execute Sequences")
            print("2. ⚡ Quick Execute (with smart defaults)")
            print("3. 🛠️  Advanced Sequence Builder")
        else:
            print("1. 🔧 Generate Template Files (Required First Step)")
            print("2. 🔍 Browse Available Generators")
            print("3. ⚡ Quick Execute (Limited - Stage 3 only)")

        print("4. 📊 View Available Models")
        print("5. 📁 Recent Executions")
        print("6. 🔧 System Information")
        print("q. 🚪 Quit")

        choice = input("\n👉 Select option: ").strip().lower()
        return choice

    async def handle_choice(self, choice):
        """Handle menu selection."""
        # Different handlers based on catalog availability
        if self.catalog and self.catalog.get("templates"):
            handlers = {
                '1': self.browse_sequences,
                '2': self.quick_execute,
                '3': self.advanced_builder,
                '4': self.view_models,
                '5': self.recent_executions,
                '6': self.system_info,
                'q': self.quit_handler
            }
        else:
            handlers = {
                '1': self.generate_templates,
                '2': self.browse_generators,
                '3': self.quick_execute_limited,
                '4': self.view_models,
                '5': self.recent_executions,
                '6': self.system_info,
                'q': self.quit_handler
            }

        handler = handlers.get(choice)
        if handler:
            result = await handler()
            return result
        else:
            print("❌ Invalid choice. Please try again.")

    async def browse_sequences(self):
        """Browse available sequences interactively."""
        print("\n🔍 Sequence Browser")
        print("-" * 30)

        if not self.catalog:
            print("❌ No catalog loaded")
            return

        sequences = self.catalog.get("sequences", {})
        if not sequences:
            print("❌ No sequences found")
            return

        # Group by stage for better organization
        stages = {}
        for seq_id, seq_data in sequences.items():
            stage = self.get_stage_from_id(seq_id)
            if stage not in stages:
                stages[stage] = []
            stages[stage].append((seq_id, seq_data))

        # Display by stage
        for stage in sorted(stages.keys()):
            stage_name = self.get_stage_name(stage)
            print(f"\n📁 {stage_name}")
            for seq_id, seq_data in sorted(stages[stage]):
                step_count = len(seq_data) if isinstance(seq_data, list) else 1
                print(f"  {seq_id}: {step_count} steps")

        # Allow selection
        print("\n💡 Examples: '3000', '3000:a-c', '3000|3031', 'keyword:distill'")
        selected = input("Enter sequence specification (or 'back'): ").strip()

        if selected != 'back' and selected:
            prompt = input("Enter your prompt: ").strip()
            if prompt:
                models = self.select_models()
                await self.execute_with_params(prompt, selected, models)

    def get_stage_from_id(self, seq_id):
        """Extract stage number from sequence ID."""
        return get_stage_from_id(seq_id)

    def get_stage_name(self, stage):
        """Get descriptive stage name."""
        return get_stage_name(stage)

    def select_models(self):
        """Interactive model selection using MODEL_REGISTRY directly."""
        print("\n🤖 Select Models:")

        # Get the actual system default
        system_default = get_default_model()

        # Build menu directly from MODEL_REGISTRY
        menu_options = []
        option_models = []

        # Add all models from MODEL_REGISTRY
        for model_name in MODEL_REGISTRY.keys():
            default_marker = " (default)" if model_name == system_default else ""
            menu_options.append(f"{model_name}{default_marker}")
            option_models.append([model_name])

        # Add multiple models option
        menu_options.append("Multiple models")
        option_models.append("multiple")  # Special case

        # Add custom option
        menu_options.append("Custom")
        option_models.append(None)  # Special case for custom

        # Display menu
        for i, option in enumerate(menu_options, 1):
            print(f"{i}. {option}")

        choice = input(f"Choice (1-{len(menu_options)}, default=1): ").strip() or "1"

        try:
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(option_models):
                selected = option_models[choice_idx]

                if selected is None:  # Custom option
                    models_str = input("Enter comma-separated models: ").strip()
                    return [m.strip() for m in models_str.split(",") if m.strip()]
                elif selected == "multiple":  # Multiple models
                    return self._select_multiple_models()
                else:
                    return selected
        except ValueError:
            pass

        # Fallback to system default
        return [system_default]

    def _select_multiple_models(self):
        """Select multiple models from MODEL_REGISTRY."""
        print("\n📋 Select multiple models (comma-separated numbers):")

        models_list = list(MODEL_REGISTRY.keys())
        for i, model in enumerate(models_list, 1):
            print(f"{i}. {model}")

        choices = input("\nEnter numbers (e.g., 1,3,5): ").strip()
        selected_models = []

        try:
            for choice in choices.split(","):
                idx = int(choice.strip()) - 1
                if 0 <= idx < len(models_list):
                    selected_models.append(models_list[idx])
        except ValueError:
            pass

        if selected_models:
            return selected_models
        else:
            # Fallback to system default
            return [get_default_model()]

    async def quick_execute(self):
        """Quick execution with smart defaults."""
        print("\n⚡ Quick Execute")
        print("-" * 20)
        print("💡 You can embed specs in your prompt:")
        print("   [SEQ:3000|3031] [MODEL:gpt-4o,claude-3-sonnet] Your prompt here")

        # Get prompt
        prompt = input("\nEnter your prompt: ").strip()
        if not prompt:
            print("❌ Prompt required")
            return

        # Check for embedded sequence/model specs
        cleaned_prompt, seq_spec, models_list = PromptParser.extract_all_from_prompt(prompt)

        # Use smart defaults if not embedded
        if not seq_spec:
            print("\n🎯 No sequence specified. Recommended sequences:")
            print(f"  {DEFAULT_SEQUENCES['general']} - Intent distiller (general purpose)")
            print(f"  {DEFAULT_SEQUENCES['analysis']} - Problem exploder (analysis)")
            print(f"  {DEFAULT_SEQUENCES['enhancement']} - Prompt enhancer (improvement)")
            default_seq = DEFAULT_SEQUENCES['general']
            seq_spec = input(f"Enter sequence (default={default_seq}): ").strip() or default_seq

        if not models_list:
            # Use actual default from centralized config
            default_model = get_default_model()
            models_list = [default_model]

        print(f"\n📝 Prompt: {cleaned_prompt}")
        print(f"🔗 Sequence: {seq_spec}")
        print(f"🤖 Models: {', '.join(models_list)}")

        confirm = input("\n✅ Execute? (Y/n): ").strip().lower()
        if confirm != 'n':
            await self.execute_with_params(cleaned_prompt, seq_spec, models_list)

    async def execute_with_params(self, prompt, sequence_spec, models):
        """Execute using existing execution engine."""
        try:
            print(f"\n🚀 Executing sequence '{sequence_spec}'...")

            # Resolve sequence using existing SequenceManager
            sequence_steps = SequenceManager.resolve_sequence_specification(
                self.catalog, sequence_spec
            )

            if not sequence_steps:
                print(f"❌ Sequence '{sequence_spec}' not found")
                return

            print(f"📋 Found {len(sequence_steps)} steps")

            # Show execution parameters with rich
            exec_params = {
                "sequence": sequence_spec,
                "models": str(models),
                "minified-output": False
            }
            print_params(exec_params, "Execution Parameters")

            display_params = {
                "show-inputs": True,
                "show-system-instructions": True,
                "show-responses": True
            }
            print_params(display_params, "Output Display Options")

            sequence_params = {
                "chain-mode": True,
                "aggregator": "None",
                "aggregator-inputs": "[]"
            }
            print_params(sequence_params, "Sequence Execution Options")

            input_params = {
                "provider": "interactive",
                "model": models[0] if models else 'unknown',
                "sequence": sequence_spec,
                "initial_prompt": f"```{prompt}```"
            }
            print_params(input_params, "Input Parameters")

            # Add the closing block like main.py
            print('"""\n```\n')

            # Generate output file path like main.py does
            from main import PathUtils, Config
            output_dir = Config.DEFAULT_OUTPUT_DIR
            PathUtils.ensure_dir_exists(output_dir)

            # Generate filename using the same logic as main.py
            output_filename = PathUtils.generate_output_filename(
                sequence_spec, "sequence", models
            )
            output_path = PathUtils.join_path(output_dir, output_filename)

            print(f"📁 Output will be saved to: {PathUtils.get_relative_path_for_display(output_path)}")

            # Create config using existing ExecutorConfig
            config = ExecutorConfig(
                sequence_steps=sequence_steps,
                user_prompt=prompt,
                sequence_id=sequence_spec,
                models=models,
                output_file=output_path,  # Generate JSON output like main.py
                system_instruction_extractor=TemplateCatalog.get_system_instruction,
                show_inputs=EXECUTION_DEFAULTS["show_inputs"],
                show_system_instructions=EXECUTION_DEFAULTS["show_system_instructions"],
                show_responses=EXECUTION_DEFAULTS["show_responses"],
                chain_mode=EXECUTION_DEFAULTS["chain_mode"]
            )

            # Execute using existing execute_sequence function
            await execute_sequence(config)

            # Track for recent executions
            self.recent_sequences.append({
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'prompt': prompt[:50] + "..." if len(prompt) > 50 else prompt,
                'sequence': sequence_spec,
                'models': models,
                'output_file': output_path
            })

            print("\n✅ Execution completed!")
            print(f"📁 Results saved to: {PathUtils.get_relative_path_for_display(output_path)}")

        except Exception as e:
            print(f"❌ Execution failed: {e}")

    async def execute_single_sequence(self, seq_id):
        """Execute a specific sequence."""
        prompt = input(f"Enter prompt for sequence {seq_id}: ").strip()
        if prompt:
            # Use actual default from centralized config
            default_model = get_default_model()
            models = [default_model]
            await self.execute_with_params(prompt, seq_id, models)

    async def generate_templates(self):
        """Generate markdown templates from Python generators."""
        print("\n🔧 Template Generation")
        print("-" * 30)
        print("This will run Python generators to create markdown template files.")
        print("\n📁 Available stages:")
        print("  1. Generate all categorized templates")
        print("  2. Generate specific category templates")

        choice = input("\nSelect option (1-2): ").strip()

        if choice == "1":
            await self.generate_categorized_templates()
        elif choice == "2":
            await self.generate_specific_category_templates()
        else:
            print("❌ Invalid choice")

    async def generate_categorized_templates(self):
        """Generate all categorized templates."""
        print(f"\n🔧 Generating all categorized templates...")

        generators_dir = Path("src/templates/categorized_great/-700-generators")
        if not generators_dir.exists():
            print(f"❌ Generators directory not found: {generators_dir}")
            return

        # Find all Python generator files
        generator_files = list(generators_dir.glob("*.py"))
        if not generator_files:
            print(f"❌ No generator files found in {generators_dir}")
            return

        print(f"📁 Found {len(generator_files)} generator files")

        for generator_file in generator_files:
            try:
                print(f"  🔄 Running {generator_file.name}...")
                # Run the generator
                result = os.system(f"cd src && python templates/categorized_great/-700-generators/{generator_file.name}")
                if result == 0:
                    print(f"  ✅ {generator_file.name} completed")
                else:
                    print(f"  ❌ {generator_file.name} failed")
            except Exception as e:
                print(f"  ❌ Error running {generator_file.name}: {e}")

        print(f"\n✅ All categorized template generation completed!")
        print("💡 You may need to restart the CLI to reload the catalog")

    async def generate_specific_category_templates(self):
        """Generate templates for a specific category."""
        print(f"\n🔧 Generate specific category templates...")

        base_dir = Path("src/templates/categorized_great")
        if not base_dir.exists():
            print(f"❌ Base category directory not found: {base_dir}")
            return

        # Discover available categories dynamically (exclude the generators folder)
        categories = [
            p.name for p in sorted(base_dir.iterdir())
            if p.is_dir() and p.name != "-700-generators"
        ]

        if not categories:
            print("❌ No categories found")
            return

        print("Available categories:")
        for name in categories:
            print(f"  • {name}")

        category = input("\nEnter category directory name exactly as shown: ").strip()
        if not category:
            print("❌ Category required")
            return

        category_dir = base_dir / category
        if not category_dir.exists() or not category_dir.is_dir():
            print(f"❌ Category directory not found: {category_dir}")
            return

        generator_files = list(category_dir.glob("*.py"))
        if not generator_files:
            print(f"❌ No generator files found in {category_dir}")
            return

        print(f"📁 Found {len(generator_files)} generator files in {category}")

        for generator_file in generator_files:
            try:
                print(f"  🔄 Running {generator_file.name}...")
                result = os.system(f"cd src && python templates/categorized_great/{category}/{generator_file.name}")
                if result == 0:
                    print(f"  ✅ {generator_file.name} completed")
                else:
                    print(f"  ❌ {generator_file.name} failed")
            except Exception as e:
                print(f"  ❌ Error running {generator_file.name}: {e}")

        print(f"\n✅ {category} template generation completed!")
        print("💡 You may need to restart the CLI to reload the catalog")

    async def browse_generators(self):
        """Browse available generator files."""
        print("\n🔍 Available Generators")
        print("-" * 30)

        # Check main generators directory
        main_generators = Path("src/templates/categorized_great/-700-generators")
        if main_generators.exists():
            generator_files = list(main_generators.glob("*.py"))
            print(f"\n📁 Main Generators: {len(generator_files)} generators")
            for gen_file in generator_files[:5]:  # Show first 5
                print(f"  • {gen_file.name}")
            if len(generator_files) > 5:
                print(f"  ... and {len(generator_files) - 5} more")
        else:
            print("\n📁 Main Generators: Directory not found")

        # Check category-specific generators dynamically
        base_dir = Path("src/templates/categorized_great")
        if base_dir.exists():
            for category_dir in sorted(p for p in base_dir.iterdir() if p.is_dir() and p.name != "-700-generators"):
                generator_files = list(category_dir.glob("*.py"))
                if generator_files:
                    print(f"\n📁 {category_dir.name}: {len(generator_files)} generators")
                    for gen_file in generator_files[:3]:  # Show first 3
                        print(f"  • {gen_file.name}")
                    if len(generator_files) > 3:
                        print(f"  ... and {len(generator_files) - 3} more")

        input("\nPress Enter to continue...")

    async def quick_execute_limited(self):
        """Quick execute with limited catalog."""
        print("\n⚡ Quick Execute (Limited Mode)")
        print("-" * 35)
        print("⚠️  Working with partial catalog - basic sequences available")
        print("💡 Available sequences: 1000, 1404, 1405, 1406")

        prompt = input("\nEnter your prompt: ").strip()
        if not prompt:
            print("❌ Prompt required")
            return

        print("\n🎯 Recommended sequences for limited mode:")
        print("  1000 - Instruction converter")
        print("  1404 - Existential quote synthesizer")
        print("  1405 - Quote trajectory enhancer")
        print("  1406 - Quote synthesiser")

        seq_spec = input("Enter sequence (default=1000): ").strip() or "1000"
        # Use actual default from centralized config
        default_model = get_default_model()
        models = [default_model]

        print(f"\n📝 Prompt: {prompt}")
        print(f"🔗 Sequence: {seq_spec}")
        print(f"🤖 Models: {', '.join(models)}")

        confirm = input("\n✅ Execute? (Y/n): ").strip().lower()
        if confirm != 'n':
            await self.execute_with_params(prompt, seq_spec, models)

    async def advanced_builder(self):
        """Advanced sequence building."""
        print("\n🛠️ Advanced Sequence Builder")
        print("-" * 35)
        print("Build complex sequences with multiple steps and filters")
        print("\n📖 Syntax Guide:")
        print("  Single sequence:     3000")
        print("  Range filter:        3000:a-c")
        print("  Multiple sequences:  3000|3031|3100")
        print("  Keyword search:      keyword:distill")
        print("  Complex example:     3000:a-b|3031|keyword:enhance")

        sequence_spec = input("\nEnter sequence specification: ").strip()
        if not sequence_spec:
            return

        # Validate and preview
        try:
            sequence_steps = SequenceManager.resolve_sequence_specification(
                self.catalog, sequence_spec
            )

            if sequence_steps:
                print(f"\n✅ Valid sequence with {len(sequence_steps)} steps:")
                for i, (step_id, template) in enumerate(sequence_steps[:5], 1):
                    title = template.get('parts', {}).get('title', 'Unknown') if isinstance(template, dict) else 'Text step'
                    print(f"  {i}. Step {step_id}: {title}")
                if len(sequence_steps) > 5:
                    print(f"  ... and {len(sequence_steps) - 5} more steps")

                prompt = input("\nEnter prompt for this sequence: ").strip()
                if prompt:
                    models = self.select_models()
                    await self.execute_with_params(prompt, sequence_spec, models)
            else:
                print("❌ No steps found for this specification")

        except Exception as e:
            print(f"❌ Invalid sequence specification: {e}")

    async def view_models(self):
        """View available models using MODEL_REGISTRY directly."""
        print("\n📊 Available Models")
        print("-" * 25)

        # Get system default for marking
        system_default = get_default_model()

        # Group models by provider using MODEL_REGISTRY
        providers = {
            "OpenAI": ["gpt-3.5-turbo", "gpt-4", "gpt-4o", "o3-mini"],
            "Anthropic": ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku", "claude-3.7-sonnet"],
            "Google": ["gemini-pro", "gemini-flash", "gemini-2-flash", "gemini-2.5-pro"],
            "DeepSeek": ["deepseek-reasoner", "deepseek-coder", "deepseek-chat"]
        }

        for provider, models in providers.items():
            print(f"\n🏢 {provider}")
            for model in models:
                if model in MODEL_REGISTRY:
                    default_marker = " ⭐ (default)" if model == system_default else ""
                    model_id = MODEL_REGISTRY[model]
                    print(f"  • {model}{default_marker}")
                    if model != model_id:
                        print(f"    ID: {model_id}")

        input("\nPress Enter to continue...")

    async def recent_executions(self):
        """Show recent executions."""
        print("\n📁 Recent Executions")
        print("-" * 25)

        if not self.recent_sequences:
            print("No recent executions found.")
            return

        limit = INTERACTIVE_CONFIG['recent_executions_limit']
        for i, execution in enumerate(self.recent_sequences[-limit:], 1):
            print(f"{i}. {execution['timestamp']}")
            print(f"   Prompt: {execution['prompt']}")
            print(f"   Sequence: {execution['sequence']}")
            print(f"   Models: {', '.join(execution['models'])}")
            print()

        input("Press Enter to continue...")

    async def system_info(self):
        """Display system information."""
        print("\n🔧 System Information")
        print("-" * 25)

        if self.catalog:
            meta = self.catalog.get('catalog_meta', {})
            print(f"📊 Catalog Level: {meta.get('level', 'Unknown')}")
            print(f"📅 Generated: {meta.get('generated_at', 'Unknown')}")
            print(f"📁 Total Templates: {meta.get('total_templates', 0)}")
            print(f"🔗 Total Sequences: {meta.get('total_sequences', 0)}")

            stages = meta.get('stage_distribution', {})
            for stage, info in stages.items():
                print(f"\n📂 {stage.title()}: {info.get('count', 0)} templates")
                print(f"   Range: {info.get('range', 'Unknown')}")
                print(f"   Description: {info.get('description', 'Unknown')}")
        else:
            print("❌ No catalog loaded")

        print(f"\n🐍 Python: {sys.version}")
        print(f"📁 Working Directory: {os.getcwd()}")

        input("\nPress Enter to continue...")

    async def quit_handler(self):
        """Handle quit command."""
        return 'quit'


async def main():
    """Main entry point for interactive CLI."""
    cli = InteractiveCLI()
    await cli.run()


if __name__ == "__main__":
    asyncio.run(main())
